package com.example.test.service.impl;

import com.example.test.model.Book;
import com.example.test.repository.BookRepository;
import com.example.test.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class BookServiceImpl implements BookService {

    private final BookRepository bookRepository;

    @Autowired
    public BookServiceImpl(BookRepository bookRepository) {
        this.bookRepository = bookRepository;
    }

    @Override
    public List<Book> getAllBooks() {
        return bookRepository.findAll();
    }

    @Override
    public Optional<Book> getBookById(Long id) {
        return bookRepository.findById(id);
    }

    @Override
    public Book saveBook(Book book) {
        return bookRepository.save(book);
    }

    @Override
    public void deleteBook(Long id) {
        bookRepository.deleteById(id);
    }

    @Override
    public List<Book> searchBooks(String keyword, String searchType) {
        if (keyword == null || keyword.isEmpty()) {
            return Collections.emptyList();
        }
        
        switch (searchType) {
            case "title":
                return bookRepository.findByTitleContainingIgnoreCase(keyword);
            case "author":
                return bookRepository.findByAuthorContainingIgnoreCase(keyword);
            case "category":
                return bookRepository.findByCategoryContainingIgnoreCase(keyword);
            default:
                return Collections.emptyList();
        }
    }
} 