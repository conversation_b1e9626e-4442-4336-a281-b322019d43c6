package com.example.test.config;

import com.example.test.model.Book;
import com.example.test.repository.BookRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

@Component
public class DataInitializer implements CommandLineRunner {

    private final BookRepository bookRepository;

    @Autowired
    public DataInitializer(BookRepository bookRepository) {
        this.bookRepository = bookRepository;
    }

    @Override
    public void run(String... args) {
        // 添加示例数据
        Book book1 = new Book();
        book1.setTitle("Java编程思想");
        book1.setAuthor("<PERSON>");
        book1.setIsbn("978-7111213826");
        book1.setPublisher("机械工业出版社");
        book1.setPublishDate(createDate(2007, 6, 1));
        book1.setCategory("编程");
        book1.setPages(880);
        book1.setDescription("Java学习经典书籍，全面介绍了Java编程的核心概念和技术。");
        book1.setStock(10);

        Book book2 = new Book();
        book2.setTitle("Spring实战");
        book2.setAuthor("Craig Walls");
        book2.setIsbn("978-7115417305");
        book2.setPublisher("人民邮电出版社");
        book2.setPublishDate(createDate(2016, 4, 1));
        book2.setCategory("编程");
        book2.setPages(550);
        book2.setDescription("全面介绍Spring框架的使用和最佳实践。");
        book2.setStock(8);

        Book book3 = new Book();
        book3.setTitle("三体");
        book3.setAuthor("刘慈欣");
        book3.setIsbn("978-7536692930");
        book3.setPublisher("重庆出版社");
        book3.setPublishDate(createDate(2008, 1, 1));
        book3.setCategory("科幻");
        book3.setPages(302);
        book3.setDescription("中国科幻文学的代表作品，讲述了地球文明与三体文明的相遇。");
        book3.setStock(15);

        Book book4 = new Book();
        book4.setTitle("活着");
        book4.setAuthor("余华");
        book4.setIsbn("978-7506365437");
        book4.setPublisher("作家出版社");
        book4.setPublishDate(createDate(2012, 8, 1));
        book4.setCategory("文学");
        book4.setPages(226);
        book4.setDescription("讲述了农村人福贵悲惨的人生，展示了中国人在困境中的坚强。");
        book4.setStock(12);

        Book book5 = new Book();
        book5.setTitle("深入理解计算机系统");
        book5.setAuthor("Randal E. Bryant");
        book5.setIsbn("978-7111321330");
        book5.setPublisher("机械工业出版社");
        book5.setPublishDate(createDate(2011, 1, 1));
        book5.setCategory("计算机");
        book5.setPages(712);
        book5.setDescription("计算机科学经典教材，全面介绍计算机系统的基本概念和原理。");
        book5.setStock(6);

        bookRepository.saveAll(Arrays.asList(book1, book2, book3, book4, book5));
    }
    
    private Date createDate(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, day);
        return calendar.getTime();
    }
} 