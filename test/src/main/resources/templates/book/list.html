<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/main :: layout('图书列表', ~{::content})}">
<body>
    <div th:fragment="content">
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="mb-4">
                    <i class="fas fa-book me-2"></i>图书列表
                    <span class="badge bg-warning ms-2">开心阅读!</span>
                </h2>
            </div>
            <div class="col-md-4">
                <div class="card search-card">
                    <div class="card-body">
                        <form th:action="@{/books/search}" method="get" class="d-flex">
                            <select name="searchType" class="form-select me-2" style="width: 120px;">
                                <option value="title" th:selected="${searchType == 'title'}">书名</option>
                                <option value="author" th:selected="${searchType == 'author'}">作者</option>
                                <option value="category" th:selected="${searchType == 'category'}">分类</option>
                            </select>
                            <input type="text" name="keyword" class="form-control me-2" placeholder="搜索..." th:value="${keyword}">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4 book-list-card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>我的图书收藏
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag me-1"></i>ID</th>
                                <th><i class="fas fa-book me-1"></i>书名</th>
                                <th><i class="fas fa-user me-1"></i>作者</th>
                                <th><i class="fas fa-tag me-1"></i>分类</th>
                                <th><i class="fas fa-building me-1"></i>出版社</th>
                                <th><i class="fas fa-cubes me-1"></i>库存</th>
                                <th><i class="fas fa-tools me-1"></i>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:if="${books.empty}">
                                <td colspan="7" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-info-circle fa-3x mb-3"></i>
                                        <p>暂无图书记录</p>
                                        <p class="text-muted">快来添加你喜欢的书吧！</p>
                                    </div>
                                </td>
                            </tr>
                            <tr th:each="book : ${books}" class="book-row">
                                <td th:text="${book.id}">1</td>
                                <td>
                                    <strong th:text="${book.title}">Java编程思想</strong>
                                </td>
                                <td th:text="${book.author}">Bruce Eckel</td>
                                <td>
                                    <span class="badge bg-info" th:text="${book.category}">编程</span>
                                </td>
                                <td th:text="${book.publisher}">机械工业出版社</td>
                                <td>
                                    <span th:class="${book.stock > 5 ? 'badge bg-success' : 'badge bg-danger'}"
                                          th:text="${book.stock}">10</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a th:href="@{/books/{id}(id=${book.id})}" class="btn btn-info" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a th:href="@{/books/{id}/edit(id=${book.id})}" class="btn btn-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a th:href="@{/books/{id}/delete(id=${book.id})}" 
                                           class="btn btn-danger"
                                           onclick="return confirm('确定要删除这本书吗？')" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <a href="/books/new" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-1"></i>添加新图书
        </a>
        
        <style>
            .empty-state {
                padding: 30px;
                text-align: center;
                color: var(--text-color);
            }
            
            .search-card {
                border-color: var(--accent-color);
                transform: rotate(-1deg);
            }
            
            .book-list-card {
                position: relative;
            }
            
            .book-list-card::after {
                content: "📚";
                position: absolute;
                top: -15px;
                right: -5px;
                font-size: 2rem;
                transform: rotate(15deg);
            }
            
            .book-row {
                transition: all 0.3s;
            }
            
            .book-row:hover {
                transform: scale(1.01);
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                position: relative;
                z-index: 10;
            }
        </style>
    </div>
</body>
</html> 