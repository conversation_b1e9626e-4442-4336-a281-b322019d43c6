<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/main :: layout(${book.id == null ? '添加图书' : '编辑图书'}, ~{::content})}">
<body>
    <div th:fragment="content">
        <div class="card form-card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    <span th:text="${book.id == null ? '添加新图书' : '编辑图书'}">添加/编辑图书</span>
                </h3>
            </div>
            <div class="card-body">
                <form th:action="@{/books}" th:object="${book}" method="post" class="needs-validation" novalidate>
                    <input type="hidden" th:field="*{id}" />
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="title" class="form-label">
                                    <i class="fas fa-heading me-1"></i>书名
                                </label>
                                <input type="text" class="form-control" id="title" th:field="*{title}" required>
                                <div class="invalid-feedback">请输入书名</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="author" class="form-label">
                                    <i class="fas fa-user me-1"></i>作者
                                </label>
                                <input type="text" class="form-control" id="author" th:field="*{author}" required>
                                <div class="invalid-feedback">请输入作者</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="isbn" class="form-label">
                                    <i class="fas fa-barcode me-1"></i>ISBN
                                </label>
                                <input type="text" class="form-control" id="isbn" th:field="*{isbn}" required>
                                <div class="invalid-feedback">请输入ISBN</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="publisher" class="form-label">
                                    <i class="fas fa-building me-1"></i>出版社
                                </label>
                                <input type="text" class="form-control" id="publisher" th:field="*{publisher}" required>
                                <div class="invalid-feedback">请输入出版社</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="publishDate" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>出版日期
                                </label>
                                <input type="date" class="form-control" id="publishDate" th:field="*{publishDate}" required>
                                <div class="invalid-feedback">请选择出版日期</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="category" class="form-label">
                                    <i class="fas fa-tag me-1"></i>分类
                                </label>
                                <input type="text" class="form-control" id="category" th:field="*{category}" required>
                                <div class="invalid-feedback">请输入分类</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="pages" class="form-label">
                                    <i class="fas fa-file-alt me-1"></i>页数
                                </label>
                                <input type="number" class="form-control" id="pages" th:field="*{pages}" required min="1">
                                <div class="invalid-feedback">请输入有效的页数</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-cartoon">
                                <label for="stock" class="form-label">
                                    <i class="fas fa-cubes me-1"></i>库存
                                </label>
                                <input type="number" class="form-control" id="stock" th:field="*{stock}" required min="0">
                                <div class="invalid-feedback">请输入有效的库存数量</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-group-cartoon">
                            <label for="description" class="form-label">
                                <i class="fas fa-info-circle me-1"></i>描述
                            </label>
                            <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/books" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <style>
            .form-card {
                position: relative;
                overflow: visible;
            }
            
            .form-card::before {
                content: "✏️";
                position: absolute;
                top: -15px;
                left: -5px;
                font-size: 2rem;
                transform: rotate(-15deg);
            }
            
            .form-group-cartoon {
                position: relative;
                margin-bottom: 10px;
                transition: all 0.3s;
            }
            
            .form-group-cartoon:hover {
                transform: translateY(-5px);
            }
            
            .form-group-cartoon label {
                background-color: var(--accent-color);
                color: var(--text-color);
                padding: 5px 15px;
                border-radius: 50px;
                display: inline-block;
                margin-bottom: 8px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                position: relative;
                left: 10px;
                transform: rotate(-2deg);
            }
            
            .form-control, .form-select {
                border-width: 3px;
                transition: all 0.3s;
            }
            
            .form-control:focus, .form-select:focus {
                transform: scale(1.02);
            }
            
            @keyframes pulse {
                0% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.05);
                }
                100% {
                    transform: scale(1);
                }
            }
            
            .btn-primary {
                animation: pulse 2s infinite;
            }
        </style>
        
        <script>
            // 表单验证
            (function() {
                'use strict';
                
                document.addEventListener('DOMContentLoaded', function() {
                    const forms = document.querySelectorAll('.needs-validation');
                    
                    Array.prototype.slice.call(forms).forEach(function(form) {
                        form.addEventListener('submit', function(event) {
                            if (!form.checkValidity()) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            
                            form.classList.add('was-validated');
                        }, false);
                    });
                    
                    // 添加动画效果
                    const inputs = document.querySelectorAll('.form-control, .form-select');
                    inputs.forEach(function(input, index) {
                        input.addEventListener('focus', function() {
                            this.parentElement.style.zIndex = "10";
                        });
                        
                        input.addEventListener('blur', function() {
                            this.parentElement.style.zIndex = "1";
                        });
                    });
                });
            })();
        </script>
    </div>
</body>
</html> 