<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/main :: layout('图书详情', ~{::content})}">
<body>
    <div th:fragment="content">
        <div class="card mb-4 book-detail-card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-book me-2"></i>图书详情
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center mb-4 book-cover-container">
                            <div class="book-cover">
                                <div class="book-cover-inner">
                                    <img src="https://via.placeholder.com/300x400?text=Book+Cover" class="img-fluid" alt="Book Cover">
                                </div>
                            </div>
                            <div class="mt-3">
                                <span th:class="${book.stock > 5 ? 'badge bg-success' : 'badge bg-danger'}" style="font-size: 1rem;">
                                    <i class="fas fa-cubes me-1"></i>库存: <span th:text="${book.stock}">10</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h2 class="mb-3 book-title" th:text="${book.title}">Java编程思想</h2>
                        
                        <div class="mb-3">
                            <span class="badge bg-info me-2" th:text="${book.category}">编程</span>
                        </div>
                        
                        <div class="book-info-card">
                            <table class="table table-striped">
                                <tbody>
                                    <tr>
                                        <th style="width: 150px;"><i class="fas fa-user me-2"></i>作者</th>
                                        <td th:text="${book.author}">Bruce Eckel</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fas fa-barcode me-2"></i>ISBN</th>
                                        <td th:text="${book.isbn}">978-7111213826</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fas fa-building me-2"></i>出版社</th>
                                        <td th:text="${book.publisher}">机械工业出版社</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fas fa-calendar me-2"></i>出版日期</th>
                                        <td th:text="${#dates.format(book.publishDate, 'yyyy-MM-dd')}">2007-06-01</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fas fa-file-alt me-2"></i>页数</th>
                                        <td th:text="${book.pages}">880</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card mb-4 description-card">
                            <div class="card-header">
                                <i class="fas fa-info-circle me-2"></i>图书简介
                            </div>
                            <div class="card-body">
                                <p th:text="${book.description}" class="mb-0">Java学习经典书籍，全面介绍了Java编程的核心概念和技术。</p>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="/books" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <div>
                                <a th:href="@{/books/{id}/edit(id=${book.id})}" class="btn btn-warning btn-lg me-2">
                                    <i class="fas fa-edit me-1"></i>编辑
                                </a>
                                <a th:href="@{/books/{id}/delete(id=${book.id})}" 
                                   class="btn btn-danger btn-lg"
                                   onclick="return confirm('确定要删除这本书吗？')">
                                    <i class="fas fa-trash me-1"></i>删除
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
            .book-detail-card {
                overflow: visible;
            }
            
            .book-cover-container {
                position: relative;
                padding: 20px;
            }
            
            .book-cover {
                position: relative;
                perspective: 1000px;
                transform-style: preserve-3d;
                transition: all 0.5s;
            }
            
            .book-cover:hover {
                transform: rotateY(15deg);
            }
            
            .book-cover-inner {
                border-radius: 5px;
                box-shadow: 5px 5px 20px rgba(0,0,0,0.3);
                overflow: hidden;
                transform-style: preserve-3d;
                position: relative;
            }
            
            .book-cover-inner::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                width: 30px;
                background: linear-gradient(to left, rgba(0,0,0,0.2), rgba(0,0,0,0));
                transform: rotateY(90deg);
                transform-origin: right;
            }
            
            .book-cover-inner img {
                display: block;
            }
            
            .book-title {
                position: relative;
                display: inline-block;
                padding-bottom: 5px;
            }
            
            .book-title::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 3px;
                background: var(--accent-color);
                border-radius: 10px;
            }
            
            .book-info-card {
                border: 2px dashed var(--secondary-color);
                border-radius: 15px;
                padding: 10px;
                margin-bottom: 20px;
                background-color: rgba(255, 255, 255, 0.7);
            }
            
            .description-card {
                border-color: var(--accent-color);
                transform: rotate(-1deg);
            }
            
            .description-card .card-header {
                background-color: var(--accent-color);
            }
            
            .description-card .card-body {
                background-color: rgba(255, 209, 102, 0.1);
                font-style: italic;
                border-radius: 0 0 15px 15px;
            }
        </style>
    </div>
</body>
</html> 