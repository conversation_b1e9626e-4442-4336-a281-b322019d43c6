<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:fragment="layout(title, content)">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - 图书管理系统'">图书管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comic+Neue:wght@400;700&family=Bubblegum+Sans&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #FF6B6B;
            --secondary-color: #4ECDC4;
            --accent-color: #FFD166;
            --background-color: #F7FFF7;
            --text-color: #2C3E50;
            --danger-color: #FF6B6B;
            --success-color: #6BFF9E;
            --warning-color: #FFD166;
            --info-color: #6BDFFF;
        }
        
        body {
            background-color: var(--background-color);
            font-family: 'Comic Neue', cursive;
            color: var(--text-color);
            background-image: url('https://www.transparenttextures.com/patterns/cartographer.png');
        }
        
        h1, h2, h3, h4, h5, h6, .navbar-brand {
            font-family: 'Bubblegum Sans', cursive;
        }
        
        .navbar {
            background-color: var(--primary-color);
            border-bottom: 4px solid var(--accent-color);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .navbar::before {
            content: "";
            position: absolute;
            top: -10px;
            left: 0;
            right: 0;
            height: 10px;
            background: repeating-linear-gradient(
                45deg,
                var(--accent-color),
                var(--accent-color) 10px,
                var(--secondary-color) 10px,
                var(--secondary-color) 20px
            );
        }
        
        .navbar-brand {
            font-weight: bold;
            color: white !important;
            font-size: 1.8rem;
            text-shadow: 2px 2px 0 var(--text-color);
        }
        
        .nav-link {
            color: white !important;
            font-weight: bold;
            transition: all 0.3s;
            border-radius: 20px;
            padding: 8px 15px !important;
            margin: 0 5px;
        }
        
        .nav-link:hover {
            background-color: var(--accent-color);
            transform: scale(1.1);
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            background-color: white;
            border: 3px solid var(--secondary-color);
        }
        
        .card:hover {
            transform: translateY(-10px) rotate(1deg);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            background-color: var(--secondary-color);
            color: white;
            border-bottom: 3px dashed white;
            font-weight: bold;
        }
        
        .btn {
            border-radius: 50px;
            font-weight: bold;
            padding: 8px 20px;
            border: none;
            box-shadow: 0 4px 0 rgba(0, 0, 0, 0.2);
            transition: all 0.2s;
            position: relative;
            overflow: hidden;
        }
        
        .btn:active {
            box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
            transform: translateY(3px);
        }
        
        .btn::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 1;
            }
            20% {
                transform: scale(25, 25);
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
        
        .btn-primary {
            background-color: var(--primary-color);
        }
        
        .btn-secondary {
            background-color: var(--text-color);
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-warning {
            background-color: var(--warning-color);
            color: var(--text-color);
        }
        
        .btn-info {
            background-color: var(--info-color);
        }
        
        .footer {
            background-color: var(--text-color);
            color: white;
            padding: 20px 0;
            margin-top: 50px;
            border-top: 4px solid var(--accent-color);
            position: relative;
        }
        
        .footer::before {
            content: "";
            position: absolute;
            top: -14px;
            left: 0;
            right: 0;
            height: 10px;
            background: repeating-linear-gradient(
                45deg,
                var(--accent-color),
                var(--accent-color) 10px,
                var(--secondary-color) 10px,
                var(--secondary-color) 20px
            );
        }
        
        .alert {
            border-radius: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 5px solid var(--accent-color);
            font-weight: bold;
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: 3px solid var(--secondary-color);
        }
        
        .table thead {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .table thead th {
            border-bottom: 2px dashed white;
        }
        
        .table tbody tr:hover {
            background-color: rgba(78, 205, 196, 0.1);
        }
        
        .badge {
            border-radius: 50px;
            padding: 8px 15px;
            font-weight: bold;
        }
        
        .bg-info {
            background-color: var(--info-color) !important;
            color: var(--text-color) !important;
        }
        
        .bg-success {
            background-color: var(--success-color) !important;
            color: var(--text-color) !important;
        }
        
        .bg-danger {
            background-color: var(--danger-color) !important;
            color: white !important;
        }
        
        .bg-warning {
            background-color: var(--warning-color) !important;
            color: var(--text-color) !important;
        }
        
        /* 卡通装饰元素 */
        .book-icon {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 3rem;
            color: var(--primary-color);
            animation: float 3s ease-in-out infinite;
            z-index: 100;
        }
        
        @keyframes float {
            0% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-15px) rotate(5deg);
            }
            100% {
                transform: translateY(0px) rotate(0deg);
            }
        }
        
        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid var(--secondary-color);
            padding: 10px 15px;
            font-size: 1rem;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(255, 107, 107, 0.25);
        }
        
        .form-label {
            font-weight: bold;
            color: var(--text-color);
        }
        
        /* 动画效果 */
        @keyframes bounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        
        .bounce-animation {
            animation: bounce 2s infinite;
        }
    </style>
</head>
<body>
    <!-- 卡通装饰元素 -->
    <div class="book-icon">
        <i class="fas fa-book-open"></i>
    </div>
    
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/books">
                <i class="fas fa-book-open me-2 bounce-animation"></i>图书管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/books"><i class="fas fa-list me-1"></i>图书列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/books/new"><i class="fas fa-plus me-1"></i>添加图书</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mb-5">
        <!-- 消息提示 -->
        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${message}">操作成功</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        
        <!-- 动态内容 -->
        <div th:replace="${content}">
            <!-- 这里将被替换为实际内容 -->
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer text-center">
        <div class="container">
            <p class="mb-0">© 2024 图书管理系统 - 版权所有</p>
        </div>
    </footer>

    <!-- Bootstrap JS 和 Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script>
        // 自动关闭警告框
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 3000);
            });
            
            // 添加一些卡通动画效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(function(card, index) {
                card.style.animationDelay = (index * 0.1) + 's';
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(function() {
                    card.style.transition = 'all 0.5s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 + (index * 100));
            });
        });
    </script>
</body>
</html> 