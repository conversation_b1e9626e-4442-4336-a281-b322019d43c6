spring.application.name=BookManagementSystem
server.port=8080

# H2æ°æ®åºéç½®
spring.datasource.url=jdbc:h2:mem:bookdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPAéç½®
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Thymeleaféç½®
spring.thymeleaf.cache=false
