# 图书管理系统

这是一个基于Spring Boot的图书管理系统，提供了图书的增删改查功能。

## 技术栈

- 后端：Spring Boot 2.7.14
- 数据库：H2 (内存数据库)
- 前端：HTML + CSS + JavaScript + Bootstrap 5 + Font Awesome
- 模板引擎：Thymeleaf

## 功能特点

- 图书列表展示
- 图书详情查看
- 添加新图书
- 编辑图书信息
- 删除图书
- 按书名、作者、分类搜索图书

## 运行说明

### 环境要求

- JDK 8+
- Maven 3.6+

### 运行步骤

1. 克隆项目到本地
2. 进入项目目录
3. 运行命令：`mvn spring-boot:run`
4. 访问 `http://localhost:8080` 即可使用系统

## 数据库访问

系统使用H2内存数据库，可通过以下方式访问数据库控制台：

1. 启动应用后访问 `http://localhost:8080/h2-console`
2. JDBC URL: `jdbc:h2:mem:bookdb`
3. 用户名: `sa`
4. 密码: `password`

## 项目结构

```
src/main/java/com/example/test/
├── TestApplication.java          # 应用入口
├── config/                       # 配置类
│   └── DataInitializer.java      # 数据初始化
├── controller/                   # 控制器
│   ├── BookController.java       # 图书控制器
│   └── HomeController.java       # 主页控制器
├── model/                        # 数据模型
│   └── Book.java                 # 图书实体类
├── repository/                   # 数据访问层
│   └── BookRepository.java       # 图书数据访问接口
└── service/                      # 服务层
    ├── BookService.java          # 图书服务接口
    └── impl/
        └── BookServiceImpl.java  # 图书服务实现
```

## 页面预览

- 图书列表页：展示所有图书，提供搜索功能
- 图书详情页：展示图书详细信息
- 图书表单页：用于添加和编辑图书信息 